#!/usr/bin/env python3
"""
测试增强的错误信息生成函数
"""

def generate_enhanced_error_message(exception: Exception, context: str = "") -> str:
    """
    生成增强的错误信息，确保即使原始异常信息为空也能提供有意义的描述
    
    Args:
        exception: 异常对象
        context: 错误发生的上下文描述
        
    Returns:
        str: 增强的错误信息
    """
    try:
        # 获取异常的基本信息
        exception_type = type(exception).__name__
        exception_message = str(exception).strip()
        
        # 如果异常消息为空，使用异常类型作为基础信息
        if not exception_message:
            exception_message = f"发生了 {exception_type} 异常，但未提供具体错误描述"
        
        # 构建完整的错误信息
        if context:
            error_msg = f"{context}: {exception_message} (异常类型: {exception_type})"
        else:
            error_msg = f"{exception_message} (异常类型: {exception_type})"
            
        # 限制错误信息长度，避免过长
        if len(error_msg) > 500:
            error_msg = error_msg[:497] + "..."
            
        return error_msg
        
    except Exception as e:
        # 如果生成错误信息时也出现异常，返回一个安全的默认信息
        return f"处理过程中发生未知错误，且无法获取详细错误信息 (原始异常: {type(exception).__name__})"


def test_error_handling():
    """测试各种异常情况"""
    
    print("=== 测试增强的错误信息生成函数 ===\n")
    
    # 测试1: 正常的异常信息
    try:
        raise ValueError("这是一个测试错误")
    except Exception as e:
        result = generate_enhanced_error_message(e, "文件处理")
        print(f"测试1 - 正常异常信息:")
        print(f"结果: {result}\n")
    
    # 测试2: 空异常信息
    class EmptyException(Exception):
        def __str__(self):
            return ""
    
    try:
        raise EmptyException()
    except Exception as e:
        result = generate_enhanced_error_message(e, "STRM文件生成")
        print(f"测试2 - 空异常信息:")
        print(f"结果: {result}\n")
    
    # 测试3: 只有空白字符的异常信息
    class WhitespaceException(Exception):
        def __str__(self):
            return "   \n\t  "
    
    try:
        raise WhitespaceException()
    except Exception as e:
        result = generate_enhanced_error_message(e, "资源下载")
        print(f"测试3 - 空白字符异常信息:")
        print(f"结果: {result}\n")
    
    # 测试4: 没有上下文的异常
    try:
        raise FileNotFoundError("文件不存在")
    except Exception as e:
        result = generate_enhanced_error_message(e)
        print(f"测试4 - 没有上下文:")
        print(f"结果: {result}\n")
    
    # 测试5: 超长异常信息
    try:
        long_message = "这是一个非常长的错误信息，" * 50
        raise RuntimeError(long_message)
    except Exception as e:
        result = generate_enhanced_error_message(e, "长消息测试")
        print(f"测试5 - 超长异常信息:")
        print(f"结果长度: {len(result)}")
        print(f"结果: {result}\n")
    
    print("=== 测试完成 ===")


if __name__ == "__main__":
    test_error_handling()
